import {ArrowLeftOutlined, CheckOutlined, ClearOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {defaultPaginationTableProps} from "@src/hooks";
import {Col, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import Highlighter from "react-highlight-words";
import {nhomChucNangColumns, optionTrangThaiNhomChucNangSelect, radioItemTrangThaiChucNangTable, TableNhomChucNangDataType} from "../index.configs";
import {useNhomChucNangContext} from "../index.context";
import {FormInputConfigs, initFormFields} from "./index.configs";
interface Props {
  danhSachNhomChucNang: Array<CommonExecute.Execute.IDanhSachHeThongChucNangPhanTrang>;
}

export interface IModalChiTietNhomChucNangRef {
  open: (data?: CommonExecute.Execute.IChiTietChucNang) => void;
  close: () => void;
}

type DataIndex = keyof TableNhomChucNangDataType;
const HighlighterTextSearch = Highlighter as unknown as React.FC<any>; //tạm thời vượt qua lỗi 'Highlighter' cannot be used as a JSX component...

const ModalChiTietNhomChucNang = forwardRef<IModalChiTietNhomChucNangRef, Props>(({danhSachNhomChucNang}: Props, ref) => {
  const {onUpdateNhomChucNang, loading, layChiTietNhomChucNang} = useNhomChucNangContext();
  const {ten, ma, trang_thai, stt} = FormInputConfigs;
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [chiTietChucNang, setChiTietChucNang] = useState<CommonExecute.Execute.IChiTietChucNang | null>(null);
  const searchInput = useRef<InputRef>(null);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");

  useImperativeHandle(ref, () => ({
    open: (dataChiTietChucNang?: CommonExecute.Execute.IChiTietChucNang) => {
      setIsOpen(true);
      if (dataChiTietChucNang) setChiTietChucNang(dataChiTietChucNang);
    },
    close: () => setIsOpen(false),
  }));

  // init form data gọi vào index.configs
  useEffect(() => {
    initFormFields(form, chiTietChucNang);
  }, [chiTietChucNang]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  //get màu trạng thái sử dụng
  const getStatusColor = (status: string) => {
    let color = "gray";
    if (status === "D") color = "green";
    if (status === "K") color = "red";
    return color;
  };

  const handleSearch = (selectedKeys: string[], confirm: () => void, dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters: () => void, confirm: () => void, dataIndex: DataIndex) => {
    clearFilters();
    confirm();
    setSearchText("");
    setSearchedColumn(dataIndex);
  };

  //Bấm Update
  const onPressUpdateNhomChucNang = async () => {
    try {
      const values: ReactQuery.IUpdateChucNangParams = form.getFieldsValue(); //lấy ra values của form
      await onUpdateNhomChucNang(values);
      const dataChiTiet = await layChiTietNhomChucNang(values);
      setChiTietChucNang(dataChiTiet);
    } catch (error: any) {
      console.log("onConfirm", error);
    }
  };

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onPressDeSau = () => {
    setIsOpen(false);
    setChiTietChucNang(null);
  };

  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formUpdateNhomChucNang" onClick={onPressDeSau} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onPressUpdateNhomChucNang}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn lưu thông tin?"
          buttonTitle={"   Lưu"}
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  //Render

  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableNhomChucNangDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiChucNangTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        // Xác định màu dựa vào text (trạng thái hiển thị)
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        return <Tag color={color}>{text}</Tag>;
      }
      return searchedColumn === dataIndex ? (
        <HighlighterTextSearch highlightStyle={{backgroundColor: "#96bf49", padding: 0}} searchWords={[searchText]} autoEscape textToHighlight={text ? text.toString() : ""} />
      ) : (
        text
      );
    },
  });

  // form input
  const renderFormInput = (config: any, span = 6) => {
    return (
      <Col span={span}>
        <FormInput {...config} />
      </Col>
    );
  };

  // render modal
  return (
    <Modal
      title={
        <HeaderModal
          title={chiTietChucNang ? `Chi tiết nhóm chức năng ${chiTietChucNang?.nhom_cn?.ten}` : "Tạo mới nhóm chức năng"}
          trang_thai_ten={chiTietChucNang?.nhom_cn?.trang_thai === "D" ? "Đang sử dụng" : "Ngưng sử dụng"}
          trang_thai={chiTietChucNang?.nhom_cn?.trang_thai}
        />
      }
      centered
      closable
      open={isOpen}
      onOk={() => setIsOpen(false)}
      onCancel={() => {
        form.resetFields();
        setIsOpen(false);
        setChiTietChucNang(null);
      }}
      maskClosable={false}
      width={"70vw"}
      styles={{
        body: {
          height: "68vh",
          overflow: "hidden",
          overflowX: "hidden",
        },
      }}
      className="modal-nhom-chuc-nang"
      footer={renderFooter}>
      {/* Content Modal */}
      <Form id="formUpdateNhomChucNang" onFinish={onPressUpdateNhomChucNang} form={form} layout="vertical" autoComplete="on">
        <Row gutter={16}>
          {renderFormInput(ma)}
          {renderFormInput(ten)}
          {renderFormInput(stt)}
          {renderFormInput({...trang_thai, options: optionTrangThaiNhomChucNangSelect})}
        </Row>
        <Table<TableNhomChucNangDataType>
          sticky
          bordered
          loading={loading}
          className="no-header-border-radius"
          scroll={{x: "max-content"}}
          style={{cursor: "pointer"}}
          onRow={(record, rowIndex) => {
            return {
              // onClick: async (event) => {
              //     const chiTietNhomChucNang = await layChiTietNhomChucNang({ ma: record.ma });
              //     if (chiTietNhomChucNang) {
              //         refModalChiTietNhomChucNang.current?.open(chiTietNhomChucNang);
              //     }
              // },
            };
          }}
          columns={(nhomChucNangColumns || []).map(item => {
            // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
            return {
              ...item,
              ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableNhomChucNangDataType, item.title) : {}),
            };
          })} //định nghĩa cột của table
          dataSource={danhSachNhomChucNang}
          pagination={{
            ...defaultPaginationTableProps,
            total: 10, //quan trọng - để hiển thị toàn bộ số lượng dòng dữ liệu phục vụ việc phân trang
            onChange: (page, pageSize) => {
              // onChangePage(page, pageSize)
            },
            locale: {
              jump_to: "Tới trang",
              page: "",
            },
          }}
        />
      </Form>
    </Modal>
  );
});

export default ModalChiTietNhomChucNang;

import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {NhomChucNangContext} from "./index.context";
import {NhomChucNangContextProps} from "./index.model";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/
const NhomChucNangProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachNhomChucNang, setDanhSachNhomChucNang] = useState<Array<CommonExecute.Execute.IDanhSachNhomChucNangPhanTrang>>([]);
  const [chiTietNhomChucNang, setChiTietNhomChucNang] = useState<CommonExecute.Execute.IChiTietNhomChucNang>({});
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const defaultFormValue: ReactQuery.ILayDanhSachNhomChucNangPhanTrangParams = {
    ten: "",
    ma: "",
    trang_thai: "",
    trang: 1,
    so_dong: 13,
  };

  useEffect(() => {
    // layDanhSachPhongBan()
    initData();
  }, []);

  const initData = () => {
    layDanhSachNhomChucNangPhanTrang(defaultFormValue);
  };

  //DS phòng ban phân trang - Danh sách người dùng
  const layDanhSachNhomChucNangPhanTrang = useCallback(
    async (body: ReactQuery.ILayDanhSachChucNangPhanTrangParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.GET_DANH_SACH_NHOM_CHUC_NANG_PHAN_TRANG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        setDanhSachNhomChucNang(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("layDanhSachNhomChucNangPhanTrang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //Lấy chi tiết 1 chức năng
  const layChiTietNhomChucNang = useCallback(
    async (item: ReactQuery.IChiTietNhomChucNangParams): Promise<CommonExecute.Execute.IChiTietNhomChucNang | null> => {
      try {
        const params = {
          ma: item.ma,
          actionCode: ACTION_CODE.GET_CHI_TIET_NHOM_CHUC_NANG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        setChiTietNhomChucNang(responseData.data);
        return responseData.data as CommonExecute.Execute.IChiTietChucNang;
      } catch (error: any) {
        console.log("layChiTietChucNang error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  const onSyncChucNang = useCallback(async () => {
    try {
      const params = {
        actionCode: ACTION_CODE.SYNC_CHUC_NANG,
      };
      const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if (responseData && (responseData.data as unknown as number) === -1) {
        message.success("Đồng bộ chức năng thành công!");
        initData();
      }
    } catch (error: any) {
      console.log("onSyncChucNang error ", error.message || error);
    }
  }, [mutateUseCommonExecute]);

  //Cập nhật hoặc tạo mới 1 phòng ban
  const onUpdateNhomChucNang = useCallback(
    async (body: ReactQuery.IUpdateChucNangParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LUU_CAP_NHAT_CHUC_NANG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
        }
      } catch (error: any) {
        console.log("onUpdateNhomChucNang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<NhomChucNangContextProps>(
    () => ({
      danhSachNhomChucNang: danhSachNhomChucNang,
      loading: mutateUseCommonExecute.isLoading,
      onUpdateNhomChucNang: onUpdateNhomChucNang,
      tongSoDong,
      layDanhSachNhomChucNangPhanTrang: layDanhSachNhomChucNangPhanTrang,
      layChiTietNhomChucNang: layChiTietNhomChucNang,
      defaultFormValue,
      onSyncChucNang: onSyncChucNang,
    }),
    [danhSachNhomChucNang, mutateUseCommonExecute, onUpdateNhomChucNang, tongSoDong, layDanhSachNhomChucNangPhanTrang, layChiTietNhomChucNang, onSyncChucNang],
  );

  return <NhomChucNangContext.Provider value={value}>{children}</NhomChucNangContext.Provider>;
};

export default NhomChucNangProvider;

import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";
import {LabeledValue} from "antd/es/select";

export interface TableNhomChucNangDataType {
  stt?: number;
  ma?: string;
  ten?: string;
  ten_nhom?: string;
  sl_chuc_nang?: number;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
  key?: string;
}

const onHeaderCell = () => ({
  className: "header-cell-custom",
});
// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const nhomChucNangColumns: TableProps<TableNhomChucNangDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center"},
  {...defaultTableColumnsProps, title: "Mã", dataIndex: "ma", key: "ma", width: 200, align: "center"},
  {...defaultTableColumnsProps, title: "Tên", dataIndex: "ten", key: "ten", width: 250, align: "left"},
  {...defaultTableColumnsProps, title: "SL c.năng", dataIndex: "sl_chuc_nang", key: "sl_chuc_nang", width: 120, align: "center"},
  {...defaultTableColumnsProps, title: "TT h.thị", dataIndex: "stt", key: "stt", width: 120, align: "center"},
  {...defaultTableColumnsProps, title: "Ngày Tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: 200, align: "center"},
  {...defaultTableColumnsProps, title: "Người Tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: 200, align: "center"},
  {...defaultTableColumnsProps, title: "Ngày Cập Nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: 200, align: "center"},
  {...defaultTableColumnsProps, title: "Người Cập Nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: 200, align: "center"},
  {...defaultTableColumnsProps, title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 120, align: "center"},
];

export const setFormFields = (form: any, chiTietNhomChucNang: any) => {
  if (chiTietNhomChucNang) {
    form.setFields([
      {
        name: "ten",
        value: chiTietNhomChucNang.ten || "",
      },
      {
        name: "ma",
        value: chiTietNhomChucNang.ma || "",
      },
      {
        name: "loai",
        value: chiTietNhomChucNang.loai || "",
      },
      {
        name: "kieu_ad",
        value: chiTietNhomChucNang.kieu_ad || "",
      },
    ]);
  }
};

export const radioItemTrangThaiChucNangTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//option select trạng thái
export const optionTrangThaiNhomChucNangSelect = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

//Form
export interface IFormTimKiemPhanTrangNhomChucNangFieldsConfig {
  ten: IFormInput;
  ma: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemPhanTrangNhomChucNang: IFormTimKiemPhanTrangNhomChucNangFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã nhóm chức năng",
    placeholder: "Nhập mã nhóm chức năng",
    className: "!mb-0",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên nhóm chức năng",
    placeholder: "Nhập tên nhóm chức năng",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};
//option select loại chức năng
export const optionLoaiChucNangData = [
  {value: "NHAP", label: "NHAP"},
  {value: "XEM", label: "XEM"},
];
//option select kiểu áp dụng
export const optionLoaiKieuApDungData = [
  {value: "K", label: "AD riêng cho từng tài khoản"},
  {value: "C", label: "AD chung cho tất cả tài khoản"},
];

import React from "react";
import R from "@R";
import {BO_MA_MENU} from "@src/configs/router";
import {COLOR_PALETTE} from "@src/constants";
import {useMenuNguoiDung} from "@src/hooks/menuNguoiDungStore";
import {navigateTo, onChangeAlias} from "@src/utils";
import {getMenuHeThong} from "@src/utils/initData";
import {getMenuIcon, createIconMappingFromMenuData} from "@src/utils/getMenuIcon";
import {Menu} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";
import {memo, useCallback, useMemo, useState, useEffect} from "react";
import {NavLink, useLocation} from "react-router-dom";
import "./index.default.scss";
import {ReactQuery} from "@src/@types";

interface SideNavProps {
  color?: string;
  isCollapsed?: boolean;
}

const {SubMenu} = Menu;
const Sidenav = ({color, isCollapsed = false}: SideNavProps) => {
  const {pathname} = useLocation();
  const {menuNguoiDung} = useMenuNguoiDung();

  //list những menu cha đang được active
  const [currentSection, setCurrentSection] = useState(["MN20240800000002"]);

  /* ĐỆ QUY ĐỂ LẤY CHI NHÁNH CON */
  const deQuyChiLayMenuCon = useCallback((chiNhanhCha: any, listTimKiem: any) => {
    const listConFilter = listTimKiem.filter((item: any) => item.ma_cha === chiNhanhCha.ma);
    const listConLai = listTimKiem.filter((item: any) => item.ma_cha !== chiNhanhCha.ma);

    // Đảm bảo menu con có thuộc tính label
    listConFilter.forEach((item: any) => {
      if (!item.label) {
        item.label = item.ten;
      }
    });

    if (listConFilter.length > 0 && listConLai.length === 0) return listConFilter;
    if (listConLai.length === 0) return [];
    else {
      for (let i = 0; i < listConFilter.length; i++) listConFilter[i].items = deQuyChiLayMenuCon(listConFilter[i], listConLai);
      return listConFilter;
    }
  }, []);

  const listMenuFormat = useMemo(() => {
    try {
      // Log menuNguoiDung để xem dữ liệu thực tế
      console.log("Total items:", menuNguoiDung.length);
      menuNguoiDung.forEach((item, index) => {
        console.log(`${index + 1}. Menu:`, {
          ma: item.ma,
          ten: item.ten,
          url: item.url,
          hasSubItems: !!(item as any).items,
        });
        // Check if item has subitems (cast to any to access items property)
        const itemWithItems = item as any;
        if (itemWithItems.items && itemWithItems.items.length > 0) {
          itemWithItems.items.forEach((subItem: any, subIndex: number) => {
            console.log(`   ${index + 1}.${subIndex + 1} SubMenu:`, {
              ma: subItem.ma,
              ten: subItem.ten,
              label: subItem.label,
              displayText: subItem.label || subItem.ten,
              url: subItem.url,
            });
          });
        }
      });

      // Create icon mapping based on actual menu data
      createIconMappingFromMenuData(menuNguoiDung);

      // let listMenuFormat = menuNguoiDung;
      const listMenuFormat: Array<CommonExecute.Execute.IMenuNguoiDungTheoNhom & {items: Array<any>; key: string; label?: string; icon: any}> = menuNguoiDung.map(item => {
        // xử lý url trả ra từ server là # -> dẫn đến việc khi map vào page sẽ bị thêm class active -> lỗi
        let url = item.url;
        if (item.url === "#") {
          url = item.ten
            ?.split(" ")
            .map(itemTen => onChangeAlias(itemTen))
            .join("-");
        }
        return {
          ...item,
          url: url,
          key: item.ma,
          label: item.ten,
          icon: getMenuIcon(item.ten, url),
          items: [],
        };
      });
      //lấy ra các thằng cha to nhất + sort theo số thứ tự hiện thị
      const listMenuCha = listMenuFormat.filter(item => !item.ma_cha).sort((a, b) => a.stt - b.stt);
      for (let i = 0; i < listMenuCha.length; i++) {
        const listMenuConLai = listMenuFormat.filter(item => item.ma_cha); //lấy ra list menu con
        listMenuCha[i].items = deQuyChiLayMenuCon(listMenuCha[i], listMenuConLai);
      }
      // listMenuCha = listMenuCha.filter(item => item.items.length);//filter ra những thằng menu cha không có thằng con
      //cho menu QUẢN TRỊ HỆ THỐNG xuống dưới cùng
      const indexQuanTriHeThong = listMenuCha.findIndex(item => item.ma === BO_MA_MENU.QUAN_TRI_HE_THONG);
      if (indexQuanTriHeThong > 0) {
        const tmp = listMenuCha[indexQuanTriHeThong];
        listMenuCha[indexQuanTriHeThong] = listMenuCha[listMenuCha.length - 1];
        listMenuCha[listMenuCha.length - 1] = tmp;
      }
      return listMenuCha;
    } catch (error) {
      console.log("sortMenuChaCon error", error);
      return [];
    }
  }, [deQuyChiLayMenuCon, menuNguoiDung]);

  //list những menu cha active -> để style thằng nào active
  const rootSubmenuKeys = useMemo(() => {
    try {
      const rootSubmenuKeysTmp: Array<string> = [];
      listMenuFormat.map(item => rootSubmenuKeysTmp.push(item.ma || ""));
      return rootSubmenuKeysTmp;
    } catch (error) {
      console.log("rootSubmenuKeys1 error", error);
      return [];
    }
  }, [listMenuFormat]);

  // Function to find parent menu of active child menu
  const findParentMenuByUrl = useCallback((url: string, menuList: any[]): string | null => {
    for (const parentMenu of menuList) {
      if (parentMenu.items && parentMenu.items.length > 0) {
        for (const childMenu of parentMenu.items) {
          if (childMenu.url === url) {
            return parentMenu.ma;
          }
        }
      }
    }
    return null;
  }, []);

  // Check if current URL matches any child menu
  const isChildMenuActive = useCallback(
    (url: string, menuList: any[]): boolean => {
      return findParentMenuByUrl(url, menuList) !== null;
    },
    [findParentMenuByUrl],
  );

  // Auto-highlight parent menu when child menu is active (only on route change)
  useEffect(() => {
    if (listMenuFormat.length > 0 && isChildMenuActive(pathname, listMenuFormat)) {
      const parentMenuId = findParentMenuByUrl(pathname, listMenuFormat);

      if (parentMenuId) {
        // Only auto-open on initial load or route change, not on manual menu interactions
        setCurrentSection(prev => {
          // Don't interfere if user has manually opened/closed menus
          if (prev.length === 0 || (prev.length === 1 && prev[0] === "MN20240800000002")) {
            return [parentMenuId];
          }
          // If the parent menu is not in current selection, add it (for route changes)
          if (!prev.includes(parentMenuId)) {
            return [parentMenuId];
          }
          return prev;
        });
      }
    }
  }, [pathname, listMenuFormat, findParentMenuByUrl, isChildMenuActive]); // Remove currentSection dependency

  const dashboard = [
    <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" key={0}>
      <path d="M3 4C3 3.44772 3.44772 3 4 3H16C16.5523 3 17 3.44772 17 4V6C17 6.55228 16.5523 7 16 7H4C3.44772 7 3 6.55228 3 6V4Z" fill={color}></path>
      <path d="M3 10C3 9.44771 3.44772 9 4 9H10C10.5523 9 11 9.44771 11 10V16C11 16.5523 10.5523 17 10 17H4C3.44772 17 3 16.5523 3 16V10Z" fill={color}></path>
      <path d="M14 9C13.4477 9 13 9.44771 13 10V16C13 16.5523 13.4477 17 14 17H16C16.5523 17 17 16.5523 17 16V10C17 9.44771 16.5523 9 16 9H14Z" fill={color}></path>
    </svg>,
  ];

  const handleClick = (keys: Array<string>) => {
    const latestOpenKey = keys.find(key => currentSection.indexOf(key) === -1);
    if (rootSubmenuKeys.indexOf(latestOpenKey || "") === -1) {
      setCurrentSection(keys);
    } else {
      setCurrentSection(latestOpenKey ? [latestOpenKey] : []);
    }
  };

  const layMenuAdmin = async ({nhom}: ReactQuery.ILayDanhSachNguoiDungTheoNhomParams) => {
    getMenuHeThong({nhom});
  };

  const renderMenu = () => {
    return (
      <>
        <Menu theme="light" mode="inline" onOpenChange={handleClick} openKeys={currentSection} defaultSelectedKeys={["MN20240800000003"]}>
          {listMenuFormat.map(itemCha => (
            <SubMenu
              key={itemCha.ma}
              className={`sub-menu-custom ${itemCha.items.length === 0 && "no-arrow-submenu"}`} // nếu không có menu con thì ẩn arrow đi
              icon={React.cloneElement(getMenuIcon(itemCha.ten, itemCha.url), {
                className: "icon",
                style: {
                  background: currentSection.includes(itemCha.ma as string) ? COLOR_PALETTE.green[100] : "",
                },
              })}
              title={[
                <span key="label" className={`label ${isCollapsed ? "collapsed" : ""}`}>
                  {isCollapsed ? "" : itemCha.ten}
                </span>,
              ]}
              onTitleClick={() => {
                if (itemCha.ma === BO_MA_MENU.QUAN_TRI_HE_THONG) {
                  layMenuAdmin({nhom: "ADMIN"});
                } else if (itemCha.url && itemCha.items.length === 0) {
                  navigateTo(itemCha.url); // 👈 Điều hướng khi click SubMenu
                }
              }}>
              {itemCha.items.map((itemCon: any) => (
                <Menu.ItemGroup key={itemCon.ma}>
                  <Menu.Item key={itemCon.ma}>
                    <NavLink
                      to={itemCon.url}
                      className="ant-menu-title-label"
                      // onClick={() => {}}
                    >
                      {isCollapsed ? (
                        <PlusOutlined className="submenu-icon" />
                      ) : (
                        <>
                          <PlusOutlined className="submenu-icon-expanded" />
                          <span className="submenu-label">{itemCon.label || itemCon.ten}</span>
                        </>
                      )}
                    </NavLink>
                  </Menu.Item>
                </Menu.ItemGroup>
              ))}
            </SubMenu>
          ))}
        </Menu>
      </>
    );
  };

  return (
    <>
      <div
        className={`brand ${isCollapsed ? "collapsed" : ""}`}
        onClick={async () => {
          await layMenuAdmin({nhom: "CLIENT"});
          navigateTo("/");
        }}>
        <img src={R.images.img_logo} alt="" />
        {!isCollapsed && <span>Core bảo hiểm Dashboard</span>}
      </div>
      <hr />
      {/* MENU CŨ */}
      {/* {renderMenuCu()} */}

      {/* MENU PRO */}
      {/* {renderMenuPro()} */}

      {renderMenu()}

      {!isCollapsed && (
        <div className="aside-footer">
          <div
            className="footer-box"
            style={{
              background: color,
            }}>
            <span className="icon" style={{color}}>
              {dashboard}
            </span>
            <h6>Need Help?</h6>
            <p>Please check our docs</p>
            {/* <Button type="primary" className="ant-btn-sm ant-btn-block">
              DOCUMENTATION
            </Button> */}
          </div>
        </div>
      )}
    </>
  );
};

export default memo(Sidenav, isEqual);

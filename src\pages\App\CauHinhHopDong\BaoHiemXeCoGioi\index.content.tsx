import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {defaultPaginationTableProps, fillRowTableEmpty, useDoiTac} from "@src/hooks";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import type {FilterDropdownProps} from "antd/es/table/interface";
import dayjs from "dayjs";
import React, {memo, useCallback, useEffect, useMemo, useRef, useState} from "react";
import ModalThemHopDongBaoHiemXe, {IModalThemHopDongBaoHiemXeRef} from "./Component/ModalThemHopDongBaoHiemXeCoGioi";
import {
  FormTimKiemPhanTrangHopDongBaoHiemXeCoGioi,
  TableBaoHiemXeCoGioiDataType,
  baoHiemXeCoGioiColumns,
  optionNghiepVuSelect,
  optionTrangThaiHopDongSelect,
  radioItemTrangThaiHopDongTable,
} from "./index.configs";
import {useBaoHiemXeCoGioiContext} from "./index.context"; // file này lưu biến về state
import "./index.dark.scss";
import "./index.default.scss";
import {formatCurrencyUS, formatDateTime} from "@src/utils";
import {PAGE_SIZE} from "../../PheDuyetHopDong/PheDuyetHopDongXCG/index.configs";

type DataIndex = keyof TableBaoHiemXeCoGioiDataType;
/*
file LoginContent : xử lý phần UI của màn hình
React.Fc : khai báo rằng type LoginContent là 1 React Function
*/

const currentDate = dayjs();
const firstOfYear = dayjs().startOf("year");
const BaoHiemXeCoGioiContent: React.FC = () => {
  const {listDoiTac} = useDoiTac();
  const [formTimKiemPhanTrangHopDongBaoHiemXe] = Form.useForm();
  //   const formValues = Form.useWatch([], form);
  const {
    danhSachHopDongBaoHiemXeCoGioi,
    loading,
    layDanhSachHopDongBaoHiemXePhanTrang,
    tongSoDong,
    layChiTietHopDongBaoHiemXe,
    defaultFormValue,
    danhSachKhachHang,
    timKiemPhanTrangDoiTuongBaoHiemXe,
    listChiNhanh,
  } = useBaoHiemXeCoGioiContext();
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.ILayDanhSachHopDongXePhanTrangParams>(defaultFormValue as ReactQuery.ILayDanhSachHopDongXePhanTrangParams);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(PAGE_SIZE);
  const [listChiNhanhFiltered, setListChiNhanhFiltered] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  let refModalThemHopDongBaoHiemXe = useRef<IModalThemHopDongBaoHiemXeRef>(null);

  const {ma_doi_tac_ql, ma_chi_nhanh_ql, nv, trang_thai, ngay_d, ngay_c, nd, bien_xe} = FormTimKiemPhanTrangHopDongBaoHiemXeCoGioi;

  const dataTableListHopDongBaoHiemXe = useMemo<Array<TableBaoHiemXeCoGioiDataType>>(() => {
    try {
      const tableData = danhSachHopDongBaoHiemXeCoGioi.map((item: any, index: number) => ({
        ...item,
        stt: item.stt ?? index + 1,
        ngay_cap: formatDateTime(item.ngay_cap),
        key: index.toString(),
      }));

      // Add empty rows if data is less than 10 rows
      const arrEmptyRow: Array<TableBaoHiemXeCoGioiDataType> = fillRowTableEmpty(tableData.length);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListPhongBan error", error);
      return [];
    }
  }, [danhSachHopDongBaoHiemXeCoGioi]);

  //watch/theo dõi form value
  const watchDoiTacCapDon = Form.useWatch("ma_doi_tac_ql", formTimKiemPhanTrangHopDongBaoHiemXe);

  useEffect(() => {
    if (watchDoiTacCapDon) {
      setListChiNhanhFiltered(listChiNhanh.filter(item => item.ma_doi_tac === watchDoiTacCapDon));
    } else setListChiNhanhFiltered([]);
  }, [watchDoiTacCapDon]);

  // Khởi tạo form với giá trị mặc định
  useEffect(() => {
    formTimKiemPhanTrangHopDongBaoHiemXe.setFieldsValue({
      ngay_d: firstOfYear,
      ngay_c: currentDate,
    });
  }, []);

  // GET CHI NHÁNH THEO MÃ ĐỐI TÁC

  //search trong bảng
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  //Bấm tìm kiếm
  const onSearchApi = (values: ReactQuery.ILayDanhSachHopDongXePhanTrangParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ngay_d: values.ngay_d ? dayjs(values.ngay_d).format("YYYYMMDD") : dayjs(firstOfYear).format("YYYYMMDD"),
      ngay_c: values.ngay_d ? dayjs(values.ngay_c).format("YYYYMMDD") : dayjs(currentDate).format("YYYYMMDD"),
      bien_xe: values.bien_xe ?? "",
      nd: values.nd ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? 10,
      nv: values.nv ?? "",
      ma_doi_tac_ql: values.ma_doi_tac_ql ?? "",
      ma_chi_nhanh_ql: values.ma_chi_nhanh_ql ?? "",
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    layDanhSachHopDongBaoHiemXePhanTrang({...cleanedValues, trang: 1, so_dong: pageSize});
  };

  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      setPageSize(pageSize);
      layDanhSachHopDongBaoHiemXePhanTrang({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );

  // RENDER
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableBaoHiemXeCoGioiDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiHopDongTable : undefined,
    render: (text, record, index) => {
      if (text && dataIndex === "tong_phi") {
        return formatCurrencyUS(text);
      }

      if (dataIndex === "trang_thai_ten") {
        if (record.key.toString().includes("empty")) return "";
        const getStatusTagColor = (text: string) => {
          let color = COLOR_PALETTE.gray[70];
          if (text === "Đã duyệt") color = COLOR_PALETTE.green[100];
          else if (text === "Đã huỷ") color = COLOR_PALETTE.red[50];
          else if (text === "Đang trình duyệt") color = COLOR_PALETTE.yellow[50];
          return color;
        };
        return (
          <Tag color={getStatusTagColor(text)} className="!text-white text-[11px]">
            {text}
          </Tag>
        );
      }

      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //render header table
  const renderHeaderTableHopDongBaoHiemXe = () => {
    return (
      <div>
        <Form form={formTimKiemPhanTrangHopDongBaoHiemXe} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onSearchApi}>
          <div className="flex w-full flex-wrap items-end gap-2">
            <Row gutter={16} align="bottom" className="w-full">
              {renderFormInputColum({...ngay_d})}
              {renderFormInputColum({...ngay_c})}
              {renderFormInputColum(
                {
                  ...ma_doi_tac_ql,
                  options: listDoiTac,
                  fieldNames: {label: "ten_tat", value: "ma"},
                  onChange: (value: string) => {
                    formTimKiemPhanTrangHopDongBaoHiemXe.setFieldValue("ma_chi_nhanh_ql", undefined);
                  },
                },
                8,
              )}
              {renderFormInputColum({...ma_chi_nhanh_ql, options: listChiNhanhFiltered})}
              {renderFormInputColum({...nv, options: optionNghiepVuSelect, fieldNames: {ten: "label", ma: "value"}})}
              {renderFormInputColum({...trang_thai, options: optionTrangThaiHopDongSelect, fieldNames: {ten: "label", ma: "value"}})}
              {renderFormInputColum({...bien_xe})}
              {renderFormInputColum({...nd}, 8)}
              <Col>
                <Form.Item>
                  <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full">
                    Tìm kiếm
                  </Button>
                </Form.Item>
              </Col>
              <Col>
                <Form.Item>
                  <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalThemHopDongBaoHiemXe.current?.open()} loading={loading}>
                    Tạo mới
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    );
  };

  //render table
  return (
    <div id={ID_PAGE.BAO_HIEM_XE_CO_GIOI} className="overflow-y-hidden [&_.ant-space]:w-full">
      <Table<TableBaoHiemXeCoGioiDataType>
        sticky
        scroll={{y: "100vh"}}
        loading={loading}
        style={{cursor: "pointer"}}
        onRow={(record, rowIndex) => {
          return {
            onClick: async event => {
              if (record.key.toString().includes("empty")) return;
              const params = {so_id: record.so_id, ma_doi_tac_ql: record.ma_doi_tac_ql};
              const chiTietHopDongXe = await layChiTietHopDongBaoHiemXe(params);
              if (chiTietHopDongXe) {
                refModalThemHopDongBaoHiemXe.current?.open(chiTietHopDongXe);
              }
              const paramsDoiTuongBaoHiemXe = {
                so_id: record.so_id,
                gcn: "",
                ten: "",
                nd_tim: "",
              };
              timKiemPhanTrangDoiTuongBaoHiemXe(paramsDoiTuongBaoHiemXe);
            },
          };
        }}
        columns={(baoHiemXeCoGioiColumns || []).map(item => {
          const ARR_NO_SHOW_SEARCH_ICON = ["stt", "sl_dtuong", "vip"];
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && !ARR_NO_SHOW_SEARCH_ICON.includes(item.key.toString())
              ? getColumnSearchProps(item.key as keyof TableBaoHiemXeCoGioiDataType, item.title)
              : {}),
          };
        })} //định nghĩa cột của table
        dataSource={dataTableListHopDongBaoHiemXe}
        title={renderHeaderTableHopDongBaoHiemXe}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong, //quan trọng - để hiển thị toàn bộ số lượng dòng dữ liệu phục vụ việc phân trang
          current: page, //set current page
          pageSize: pageSize, //Số lượng mục dữ liệu trên mỗi trang
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
          locale: {
            jump_to: "Tới trang",
            page: "",
          },
        }}
        bordered
      />
      <ModalThemHopDongBaoHiemXe ref={refModalThemHopDongBaoHiemXe} danhSachKhachHang={danhSachKhachHang} />
    </div>
  );
};

export default memo(BaoHiemXeCoGioiContent);

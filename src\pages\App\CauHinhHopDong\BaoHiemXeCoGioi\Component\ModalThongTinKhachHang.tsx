import {ArrowLeftOutlined, CheckOutlined, ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {defaultPaginationTableProps, fillRowTableEmpty, useChiNhanh, useDoiTac} from "@src/hooks";
import {Col, Empty, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useBaoHiemXeCoGioiContext} from "../index.context";
import {
  dataKhachHangColumns,
  dataOptionLoaiKhachHang,
  defaultFormTimKiemPhanTrangKhachHang,
  FormTimKiemPhanTrangKhachHangConfigs,
  radioItemTrangThai<PERSON>ha<PERSON>HangTable,
  TableKhachHangDataType,
} from "./Constant";
import {useTableNoHover} from "@src/utils/tableNoHoverUtils";
interface Props {
  onSelectKhachHang: (ma_kh: CommonExecute.Execute.IKhachHang | null) => void;
}
export interface IModalThongTinKhachHangRef {
  open: (data?: CommonExecute.Execute.IKhachHang, ma_chi_nhanh_ql?: string, ma_doi_tac_ql?: string) => void;
  close: () => void;
}
const {ma_doi_tac_ql, ma_chi_nhanh_ql, nd_tim, loai_kh} = FormTimKiemPhanTrangKhachHangConfigs;
type DataIndex = keyof TableKhachHangDataType;

const PAGE_SIZE = 10; //khai báo khác default do modal nhỏ hơn

const ModalThongTinKhachHang = forwardRef<IModalThongTinKhachHangRef, Props>(({onSelectKhachHang}: Props, ref) => {
  const {listDoiTac} = useDoiTac();
  const {listChiNhanh} = useChiNhanh();

  const {loading, danhSachKhachHang, timKiemPhanTrangKhachHang, tongSoDongDataKhachHang} = useBaoHiemXeCoGioiContext();

  // Sử dụng useTableNoHover hook để tự động inject CSS và cung cấp utility functions
  const {getTableClassName: getNoHoverTableClassName, getRowClassName} = useTableNoHover({
    activeRowColor: "#96bf49", // Màu xanh lá cho row được chọn
    styleId: "modal-khach-hang-table-styles", // ID riêng cho modal này
  });

  const [formThongTinKhachHang] = Form.useForm();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [chiTietKhachHang, setChiTietKhachHang] = useState<CommonExecute.Execute.IKhachHang | null>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.ILayDanhSachKhachHangPhanTrangParams>(defaultFormTimKiemPhanTrangKhachHang);
  const [page, setPage] = useState(1);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");

  const searchInput = useRef<InputRef>(null);

  useImperativeHandle(ref, () => ({
    open: (dataChiTietKhachHang?: CommonExecute.Execute.IKhachHang, chiNhanhSelected?: string, doiTacSelected?: string) => {
      setIsOpen(true);
      if (dataChiTietKhachHang) setChiTietKhachHang(dataChiTietKhachHang);

      //đoạn này để set dữ liệu vào form và cho tìm kiếm với mã đối tác, mã chi nhánh từ ngoài truyền vào
      formThongTinKhachHang.setFieldsValue({
        ma_chi_nhanh_ql: chiNhanhSelected,
        ma_doi_tac_ql: doiTacSelected,
      });
      const bodySearch = {
        ...defaultFormTimKiemPhanTrangKhachHang,
        ma_doi_tac_ql: doiTacSelected,
        ma_chi_nhanh_ql: chiNhanhSelected,
      };
      setSearchParams(bodySearch);
      timKiemPhanTrangKhachHang(bodySearch);
    },
    close: () => setIsOpen(false),
  }));

  //MAP VALUE CỦA LIST VÀO TABLE
  const dataTableListKhachHang = useMemo<Array<TableKhachHangDataType>>(() => {
    try {
      const tableData = danhSachKhachHang.map((itemKhachHang, index) => {
        return {
          ...itemKhachHang,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TableKhachHangDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListKhachHang error", error);
      return [];
    }
  }, [danhSachKhachHang]);

  //xử lý validate form
  useEffect(() => {
    setDisableSubmit(!chiTietKhachHang); // Nếu có activeKey -> enable nút Lưu, ngược lại -> disable
  }, [chiTietKhachHang]);

  //Bấm tìm kiếm
  const onSearchApi = (values: ReactQuery.ILayDanhSachKhachHangPhanTrangParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      nd_tim: values.nd_tim ?? "",
      ma_doi_tac_ql: values.ma_doi_tac_ql ?? "",
      ma_chi_nhanh_ql: values.ma_chi_nhanh_ql ?? "",
      loai_kh: values.loai_kh ?? "",
      trang: values.trang ?? 1,
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    timKiemPhanTrangKhachHang({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
  };

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onPressDeSau = () => {
    setIsOpen(false);
    setChiTietKhachHang(null);
  };

  //bấm xác nhận
  const handleChonKhachHang = () => {
    if (chiTietKhachHang && onSelectKhachHang) {
      onSelectKhachHang(chiTietKhachHang); // Gọi callback để truyền khách hàng ra ngoài
      setIsOpen(false); // Đóng modal sau khi chọn
    }
  };

  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formUpdateNhomChucNang" onClick={onPressDeSau} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button disabled={disableSubmit} type="primary" form="formUpdateNhomChucNang" onClick={handleChonKhachHang} className="mr-2 w-40" icon={<CheckOutlined />} iconPosition="end">
          Chọn
        </Button>
      </Form.Item>
    );
  };
  //Render

  //search client
  const handleSearch = (selectedKeys: string[], confirm: () => void, dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters: () => void, confirm: () => void, dataIndex: DataIndex) => {
    clearFilters();
    confirm();
    setSearchText("");
    setSearchedColumn(dataIndex);
  };

  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      timKiemPhanTrangKhachHang({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );

  const renderFormInputColum = (props?: any, span = 5) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableKhachHangDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiKhachHangTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        // Xác định màu dựa vào text (trạng thái hiển thị)
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return <Tag color={color}>{text}</Tag>;
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  // render modal
  return (
    <div id={"MODAL_KH"}>
      <Modal
        title={"Thông tin khách hàng"}
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => {
          formThongTinKhachHang.resetFields();
          setIsOpen(false);
          setChiTietKhachHang(null);
        }}
        footer={renderFooter}
        closable
        maskClosable={false}
        width="95vw"
        style={{
          top: "4vh",
          left: 0,
          padding: 0,
        }}
        styles={{
          body: {
            height: "75vh",
            overflow: "hidden",
          },
        }}
        className="custom-full-modal">
        <Form id="form" onFinish={onSearchApi} form={formThongTinKhachHang} layout="vertical" autoComplete="on" className="mt-1">
          <Row gutter={16} align="bottom">
            {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac})}
            {renderFormInputColum({...ma_chi_nhanh_ql, options: listChiNhanh})}
            {renderFormInputColum({...loai_kh, options: dataOptionLoaiKhachHang})}
            {renderFormInputColum({...nd_tim})}
            <Col span={2} className="!mb-0">
              <Form.Item className="!mb-[8px]">
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full">
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Col>
            <Col span={2}>
              <Form.Item className="!mb-[8px]">
                <Button
                  className="w-full"
                  type="primary"
                  icon={<PlusCircleOutlined />}
                  // onClick={() => refModalThemHopDongBaoHiemXe.current?.open()}
                  loading={loading}>
                  Thêm mới
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        {dataTableListKhachHang.length > 0 ? (
          <div
            style={{
              overflow: "auto",
            }}>
            <Table<TableKhachHangDataType>
              className={getNoHoverTableClassName("custom-table no-header-border-radius")}
              sticky
              scroll={{y: "100vh"}}
              loading={loading}
              style={{cursor: "pointer"}}
              rowClassName={record => getRowClassName(chiTietKhachHang?.ma === record.ma)}
              onRow={record =>
                record.key.toString().includes("empty")
                  ? {}
                  : {
                      onClick: () => setChiTietKhachHang(record as CommonExecute.Execute.IKhachHang),
                      onDoubleClick: () => {
                        setChiTietKhachHang(record as CommonExecute.Execute.IKhachHang);
                        handleChonKhachHang();
                      },
                    }
              }
              columns={(dataKhachHangColumns || []).map(item => {
                // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
                return {
                  ...item,
                  ...(item.key && typeof item.title === "string" && item.key !== "sott" ? getColumnSearchProps(item.key as keyof TableKhachHangDataType, item.title) : {}),
                };
              })} //định nghĩa cột của table
              dataSource={dataTableListKhachHang}
              // title={'HHIhi'}
              pagination={{
                ...defaultPaginationTableProps,
                defaultPageSize: PAGE_SIZE,
                total: tongSoDongDataKhachHang, //quan trọng - để hiển thị toàn bộ số lượng dòng dữ liệu phục vụ việc phân trang
                current: page, //set current page
                pageSize: PAGE_SIZE, //Số lượng mục dữ liệu trên mỗi trang
                //được gọi khi page size change
                onChange: (page, pageSize) => {
                  onChangePage(page, pageSize);
                },
                locale: {
                  jump_to: "Tới trang",
                  page: "",
                },
              }}
              bordered
            />
          </div>
        ) : (
          <Empty description="Không có dữ liệu" />
        )}
      </Modal>
    </div>
  );
});

ModalThongTinKhachHang.displayName = "ModalThongTinKhachHang";
export default ModalThongTinKhachHang;

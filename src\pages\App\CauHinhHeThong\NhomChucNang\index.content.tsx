import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Form, Input, InputRef, Table, TableColumnType, Tag, Tooltip} from "antd";
import type {FilterDropdownProps} from "antd/es/table/interface";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import ModalChiTietNhomChucNang, {IModalChiTietNhomChucNangRef} from "./Component/ModalChiTietNhomChucNang";
import {FormTimKiemPhanTrangNhomChucNang, nhomChucNangColumns, optionTrangThaiNhomChucNangSelect, radioItemTrangThaiChucNangTable, TableNhomChucNangDataType} from "./index.configs";
import {useNhomChucNangContext} from "./index.context"; // file này lưu biến về state
import "./index.dark.scss";
import "./index.default.scss";

type DataIndex = keyof TableNhomChucNangDataType;
/*
file LoginContent : xử lý phần UI của màn hình
React.Fc : khai báo rằng type LoginContent là 1 React Function
*/
const PAGE_SIZE = 13;
const NhomChucNangContent: React.FC = () => {
  const {danhSachNhomChucNang, loading, layDanhSachNhomChucNangPhanTrang, tongSoDong, layChiTietNhomChucNang, defaultFormValue, onSyncChucNang} = useNhomChucNangContext();
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.ILayDanhSachChucNangPhanTrangParams>(defaultFormValue);
  const [page, setPage] = useState(1);

  const refModalChiTietNhomChucNang = useRef<IModalChiTietNhomChucNangRef>(null);
  const {ma, ten, trang_thai} = FormTimKiemPhanTrangNhomChucNang;

  const dataTableListPhongBan = useMemo<Array<TableNhomChucNangDataType>>(() => {
    try {
      const tableData = danhSachNhomChucNang.map((item: any, index: number) => {
        return {
          ...item,
          stt: item.stt ?? index + 1,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TableNhomChucNangDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListPhongBan error", error);
      return [];
    }
  }, [danhSachNhomChucNang]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchedColumn("");
      setSearchText("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  //Bấm tìm kiếm
  const onSearchApi = (values: ReactQuery.ILayDanhSachNhomChucNangPhanTrangParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ma: values.ma ?? "",
      ten: values.ten ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? 10,
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    layDanhSachNhomChucNangPhanTrang({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
  };

  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      layDanhSachNhomChucNangPhanTrang({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );
  //Đồng bộ chức năng
  // const handleSyncChucNang = useCallback(async () => {
  //   setPage(1);
  //   await onSyncChucNang();
  //   layDanhSachNhomChucNangPhanTrang({...searchParams, trang: page, so_dong: PAGE_SIZE});
  // }, [onSyncChucNang]);

  // RENDER
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableNhomChucNangDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiChucNangTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record?.key?.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      ); // xử lý chuyển text thành 1 dòng khi text quá dài;
    },
  });

  //form input
  const renderFormInput = (config: any, span = 5) => {
    return (
      <Col span={span}>
        <FormInput {...config} />
      </Col>
    );
  };

  //render header table
  const renderHeaderTableNhomChucNang = () => {
    return (
      <div>
        <Form layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onSearchApi}>
          <div className="flex w-full flex-wrap items-end gap-4">
            {renderFormInput({...ma})}
            {renderFormInput({...ten})}
            {renderFormInput({...trang_thai, options: optionTrangThaiNhomChucNangSelect})}

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full">
                Tìm kiếm
              </Button>
            </Form.Item>

            <Form.Item>
              <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => {}} loading={loading}>
                Thêm nhóm chức năng
              </Button>
            </Form.Item>
          </div>
        </Form>
      </div>
    );
  };

  //render table
  return (
    <div id={ID_PAGE.PHAN_NHOM_CHUC_NANG} className="[&_.ant-space]:w-full">
      <Table<TableNhomChucNangDataType>
        {...defaultTableProps}
        // sticky
        // className="antd-table-hide-scroll"
        // scroll={{y: "100vh"}}
        loading={loading}
        style={{cursor: "pointer"}}
        onRow={(record, rowIndex) => {
          return {
            onClick: async event => {
              const chiTietNhomChucNang = await layChiTietNhomChucNang({ma: record.ma});
              if (chiTietNhomChucNang) {
                refModalChiTietNhomChucNang.current?.open(chiTietNhomChucNang);
              }
            },
          };
        }}
        columns={(nhomChucNangColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableNhomChucNangDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        dataSource={dataTableListPhongBan}
        title={renderHeaderTableNhomChucNang}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong, //quan trọng - để hiển thị toàn bộ số lượng dòng dữ liệu phục vụ việc phân trang
          current: page, //set current page
          pageSize: PAGE_SIZE, //Số lượng mục dữ liệu trên mỗi trang
          //được gọi khi page size change
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
        }}
        bordered
      />
      <ModalChiTietNhomChucNang ref={refModalChiTietNhomChucNang} danhSachNhomChucNang={danhSachNhomChucNang} />
    </div>
  );
};

export default memo(NhomChucNangContent);

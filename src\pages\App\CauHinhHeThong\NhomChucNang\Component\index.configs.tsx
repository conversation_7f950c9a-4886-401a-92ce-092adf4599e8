export const initFormFields = (form: any, chiTietNhomChucNang: any) => {
  if (!chiTietNhomChucNang) return;
  const fields = Object.entries(chiTietNhomChucNang.nhom_cn).map(([name, value]) => ({
    name,
    value: value ?? "",
  }));
  form.setFields(fields);
};

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export interface IFormChiTietNhomChucNangFieldsConfig {
  stt?: number;
  ma?: string;
  ten?: string;
  ten_nhom?: string;
  sl_chuc_nang?: number;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

export const FormInputConfigs = {
  ten: {
    component: "input",
    name: "ten",
    placeholder: "Tên nhóm chức năng",
    label: "Tên nhóm chức năng", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  ma: {
    component: "input",
    name: "ma",
    placeholder: "Mã nhóm chức năng",
    label: "Mã nhóm chức năng", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
    disabled: true,
  },
  stt: {
    component: "input",
    name: "stt",
    placeholder: "Số thứ tự hiển thị",
    label: "Số thứ tự hiển thị", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    label: "Trạng thái", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
};
